[{"id": 3203362, "title": "物料需求计算", "badge": 1, "hide": false, "need": false, "letter": "matDemandCalculation", "icon": "VtjIconDatabase", "url": "/matDemandCalculation", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": [{"id": 3203363, "title": "排产单列表(纸箱)", "hide": false, "need": false, "letter": "cartonSchedulingList", "icon": null, "url": "cartonSchedulingList", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}, {"id": 3203364, "title": "排产单列表(木托板)", "hide": false, "need": false, "letter": "palletSchedulingList", "icon": null, "url": "palletSchedulingList", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}, {"id": 3203366, "title": "MRP计划列表", "hide": false, "need": false, "letter": "mrpPlanList", "icon": null, "url": "mrpPlanList", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}]}, {"id": "home", "title": "首页工作台", "url": "/ui/mask"}, {"id": "test", "title": "测试", "children": [{"id": "test_1", "title": "测试弹窗打开", "disabled": false, "type": "dialog", "url": "/ui/mask/page?id=test_1"}, {"id": "test_2", "title": "测试新开窗口", "disabled": false, "type": "window", "url": "https://www.baidu.com/"}]}, {"id": 10027, "title": "基础设置", "hide": false, "need": false, "letter": "base", "icon": "VtjIconComponents", "url": "base", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": [{"id": 3203348, "title": "物料供应周期表", "hide": false, "need": false, "letter": "matSupplyCycle", "icon": null, "url": "matSupplyCycle", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}, {"id": 3203354, "title": "供应商分单逻辑表", "hide": false, "need": false, "letter": "supSplit<PERSON>rderLogic", "icon": null, "url": "supSplit<PERSON>rderLogic", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}, {"id": 10028, "title": "辅助属性类型", "hide": false, "need": false, "letter": "mmsatType", "icon": null, "url": "mmsatType", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}, {"id": 3202359, "title": "数据看板", "hide": false, "need": false, "letter": "databoard", "icon": null, "url": "databoard", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}, {"id": 3201850, "title": "系统树结构", "hide": false, "need": false, "letter": "sysTree", "icon": null, "url": "sysTree", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}, {"id": 3202411, "title": "物料主档图片", "hide": false, "need": false, "letter": "matPicImport", "icon": null, "url": "matPicImport", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}, {"id": 3202677, "title": "用户默认首页设置", "hide": false, "need": false, "letter": "userIndex", "icon": null, "url": "userIndex", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}, {"id": 3202679, "title": "管理层数据看板", "hide": false, "need": false, "letter": "depot/dataBoard", "icon": null, "url": "depot/dataBoard", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}, {"id": 3201725, "title": "结算账户审批", "hide": false, "need": false, "letter": "bankAccountAudit", "icon": null, "url": "bankAccountAudit", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}, {"id": 3201222, "title": "库位", "hide": false, "need": false, "letter": "wms/base/storagebin", "icon": null, "url": "wms/base/storagebin", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}, {"id": 3201223, "title": "物料库位排序", "hide": false, "need": false, "letter": "wms/base/storageSort", "icon": null, "url": "wms/base/storageSort", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}, {"id": 3203283, "title": "园区对应工厂", "hide": false, "need": false, "letter": "metaDataReport/parkTofactory", "icon": null, "url": "metaDataReport/parkTofactory", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}, {"id": 10029, "title": "物料分类", "hide": false, "need": false, "letter": "materielType", "icon": null, "url": "materielType", "oinRel": false, "oinRelCode": null, "orderValue": 1, "children": null}, {"id": 3201307, "title": "物料主档（配置化）", "hide": false, "need": false, "letter": "materiel<PERSON><PERSON><PERSON>ew", "icon": null, "url": "materiel<PERSON><PERSON><PERSON>ew", "oinRel": false, "oinRelCode": null, "orderValue": 2, "children": null}, {"id": 10030, "title": "物料主档", "hide": false, "need": false, "letter": "materielMain", "icon": null, "url": "materielMain", "oinRel": false, "oinRelCode": null, "orderValue": 2, "children": null}, {"id": 10065, "title": "合同关系链", "hide": false, "need": false, "letter": "scmPurchaseContractHCreateFactory", "icon": null, "url": "scmPurchaseContractHCreateFactory", "oinRel": false, "oinRelCode": null, "orderValue": 2, "children": null}, {"id": 10069, "title": "物料编号申请", "hide": false, "need": false, "letter": "scmMaterielCodeApply", "icon": null, "url": "scmMaterielCodeApply", "oinRel": false, "oinRelCode": null, "orderValue": 3, "children": null}, {"id": 10089, "title": "物料编码申请明细", "hide": false, "need": false, "letter": "scmMaterielCodeApply/Detail", "icon": null, "url": "scmMaterielCodeApply/Detail", "oinRel": false, "oinRelCode": null, "orderValue": 4, "children": null}, {"id": 10070, "title": "物料编码申请处理", "hide": false, "need": false, "letter": "scmMaterielCodeApply/Approval", "icon": null, "url": "scmMaterielCodeApply/Approval", "oinRel": false, "oinRelCode": null, "orderValue": 5, "children": null}, {"id": 10118, "title": "物料主档处理", "hide": false, "need": false, "letter": "scmMaterielMainHandle", "icon": null, "url": "scmMaterielMainHandle", "oinRel": false, "oinRelCode": null, "orderValue": 5, "children": null}, {"id": 10031, "title": "供应商类型", "hide": false, "need": false, "letter": "scmBaseType", "icon": null, "url": "scmBaseType", "oinRel": false, "oinRelCode": null, "orderValue": 6, "children": null}, {"id": 10032, "title": "供应商档案", "hide": false, "need": false, "letter": "scmBaseFile", "icon": null, "url": "scmBaseFile", "oinRel": false, "oinRelCode": null, "orderValue": 7, "children": null}, {"id": 10098, "title": "供应商档案处理", "hide": false, "need": false, "letter": "scmBaseFileAuditList", "icon": null, "url": "/scmBaseFileAuditList", "oinRel": false, "oinRelCode": null, "orderValue": 8, "children": null}, {"id": 10213, "title": "供应商评分模板维护", "hide": false, "need": false, "letter": "supScoreTemplate", "icon": null, "url": "supScoreTemplate", "oinRel": false, "oinRelCode": null, "orderValue": 9, "children": null}, {"id": 10079, "title": "物料种类列表", "hide": false, "need": false, "letter": "metaDataReport/typeOfMaterial", "icon": null, "url": "metaDataReport/typeOfMaterial", "oinRel": false, "oinRelCode": null, "orderValue": 10, "children": null}, {"id": 10087, "title": "品牌列表", "hide": false, "need": false, "letter": "omBrand", "icon": null, "url": "omBrand", "oinRel": false, "oinRelCode": null, "orderValue": 10, "children": null}, {"id": 10066, "title": "采购员关系链", "hide": false, "need": false, "letter": "metaDataReport/purchaser", "icon": null, "url": "metaDataReport/purchaser", "oinRel": false, "oinRelCode": null, "orderValue": 11, "children": null}, {"id": 10034, "title": "采购分区与采购员档案维护", "hide": false, "need": false, "letter": "scmPurchaseCltUserInfo", "icon": null, "url": "scmPurchaseCltUserInfo", "oinRel": false, "oinRelCode": null, "orderValue": 12, "children": null}, {"id": 10055, "title": "新建物料", "hide": false, "need": false, "letter": "scmMaterielApply", "icon": null, "url": "scmMaterielApply", "oinRel": false, "oinRelCode": null, "orderValue": 99, "children": null}, {"id": 10068, "title": "计量单位列表", "hide": false, "need": false, "letter": "metaDataReport/uom", "icon": null, "url": "metaDataReport/uom", "oinRel": false, "oinRelCode": null, "orderValue": 99, "children": null}, {"id": 10088, "title": "消息模板", "hide": false, "need": false, "letter": "msgTemplate", "icon": null, "url": "msgTemplate", "oinRel": false, "oinRelCode": null, "orderValue": 99, "children": null}, {"id": 3201202, "title": "物料种类", "hide": false, "need": false, "letter": "baseMatType", "icon": null, "url": "baseMatType", "oinRel": false, "oinRelCode": null, "orderValue": 99, "children": null}, {"id": 3201266, "title": "物料分类厚度关系表", "hide": false, "need": false, "letter": "npMatgroupThicknessRelationReport", "icon": null, "url": "npMatgroupThicknessRelationReport", "oinRel": false, "oinRelCode": null, "orderValue": 99, "children": null}]}, {"id": 3201838, "title": "绩效管理", "hide": false, "need": false, "letter": "scmMerits", "icon": null, "url": "/scmMerits", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": [{"id": 3201839, "title": "考评模版", "hide": false, "need": false, "letter": "evaluateFile", "icon": null, "url": "/evaluateFile", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}, {"id": 3201840, "title": "考评计划", "hide": false, "need": false, "letter": "evaluatePlan", "icon": null, "url": "/evaluatePlan", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}, {"id": 3201843, "title": "考评中心", "hide": false, "need": false, "letter": "evaluateCenter", "icon": null, "url": "/evaluateCenter", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}, {"id": 3201844, "title": "考评事件登记", "hide": false, "need": false, "letter": "evaluateEvent", "icon": null, "url": "/evaluateEvent", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}, {"id": 3201849, "title": "考评事件跟进", "hide": false, "need": false, "letter": "evaluateEventFollow", "icon": null, "url": "/evaluateEventFollow", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}, {"id": 3201851, "title": "报表", "hide": false, "need": false, "letter": "scmMerits/report", "icon": null, "url": "/scmMerits/report", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": [{"id": 3201852, "title": "考评明细查询", "hide": false, "need": false, "letter": "scmMerits/report/npSupPerfPlanScoreDetailList", "icon": null, "url": "/scmMerits/report/npSupPerfPlanScoreDetailList", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}, {"id": 3201856, "title": "考评汇总报表", "hide": false, "need": false, "letter": "scmMerits/report/listPlanReport", "icon": null, "url": "/scmMerits/report/listPlanReport", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}, {"id": 3201866, "title": "考评供应商查询", "hide": false, "need": false, "letter": "scmMerits/report/npSupPerfPlanScoreDetaiSuplList", "icon": null, "url": "/scmMerits/report/npSupPerfPlanScoreDetaiSuplList", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}, {"id": 3201893, "title": "考评全年汇总", "hide": false, "need": false, "letter": "scmMerits/report/listSummaryReport", "icon": null, "url": "/scmMerits/report/listSummaryReport", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}]}, {"id": 3202626, "title": "月度质量验收统计", "hide": false, "need": false, "letter": "monthlyQualityAccept", "icon": null, "url": "monthlyQualityAccept", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}, {"id": 3202627, "title": "质量稳定性评分表", "hide": false, "need": false, "letter": "stabilityScore", "icon": null, "url": "stabilityScore", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}]}, {"id": 3203396, "title": "询价管理", "hide": false, "need": false, "letter": "enquiry", "icon": null, "url": "enquiry", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": [{"id": 3203397, "title": "询价中心", "hide": false, "need": false, "letter": "enquiry/enquiryCenter", "icon": null, "url": "enquiry/enquiryCenter", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}, {"id": 3203399, "title": "发布审核", "hide": false, "need": false, "letter": "enquiry/enquiryProcess", "icon": null, "url": "enquiry/enquiryProcess", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}, {"id": 3203400, "title": "比价定价", "hide": false, "need": false, "letter": "enquiry/enquiryPriceProcess", "icon": null, "url": "enquiry/enquiryPriceProcess", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}, {"id": 3203421, "title": "报表查询", "hide": false, "need": false, "letter": "enquiry/report", "icon": null, "url": "enquiry/report", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": [{"id": 3203422, "title": "询价报表", "hide": false, "need": false, "letter": "enquiry/report/enquiry", "icon": null, "url": "enquiry/report/enquiry", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}]}]}, {"id": 3201861, "title": "结算管理", "hide": false, "need": false, "letter": "clearing", "icon": null, "url": "clearing", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": [{"id": 3201862, "title": "年费结算计划", "hide": false, "need": false, "letter": "annualFeePlan", "icon": null, "url": "annualFeePlan", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}, {"id": 3201864, "title": "年费审核", "hide": false, "need": false, "letter": "annualFee<PERSON>heck", "icon": null, "url": "annualFee<PERSON>heck", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}, {"id": 3201865, "title": "结算计划收费核算", "hide": false, "need": false, "letter": "clearingPlan", "icon": null, "url": "clearingPlan", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}, {"id": 3201863, "title": "供应商年费查询", "hide": false, "need": false, "letter": "annualFee<PERSON><PERSON>y", "icon": null, "url": "annualFee<PERSON><PERSON>y", "oinRel": false, "oinRelCode": null, "orderValue": 4, "children": null}]}, {"id": 3203408, "title": "设备管理", "hide": false, "need": false, "letter": "equipment", "icon": null, "url": "equipment", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": [{"id": 3203409, "title": "设备列表", "hide": false, "need": false, "letter": "equipment/equipmentFilesList", "icon": null, "url": "equipment/equipmentFilesList", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}, {"id": 3203416, "title": "设备档案处理", "hide": false, "need": false, "letter": "equipment/equipmentFilesDispose", "icon": null, "url": "equipment/equipmentFilesDispose", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}]}, {"id": 3201876, "title": "APP通信录（已停用）", "hide": false, "need": false, "letter": "/contacts", "icon": null, "url": "/contacts", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": [{"id": 3201877, "title": "通讯录结构", "hide": false, "need": false, "letter": "contacts/struct", "icon": null, "url": "contacts/struct", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}, {"id": 3201878, "title": "通讯录管理", "hide": false, "need": false, "letter": "contacts/manage", "icon": null, "url": "contacts/manage", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}, {"id": 3201879, "title": "内部通信录分配", "hide": false, "need": false, "letter": "contacts/assign/inside", "icon": null, "url": "contacts/assign/inside", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}, {"id": 3201880, "title": "外部通信录分配", "hide": false, "need": false, "letter": "contacts/assign/outside", "icon": null, "url": "contacts/assign/outside", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}, {"id": 3201881, "title": "通讯录账号查询", "hide": false, "need": false, "letter": "contacts/account", "icon": null, "url": "contacts/account", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}]}, {"id": 3201882, "title": "京东工采", "hide": false, "need": false, "letter": "jdWork", "icon": null, "url": "/jdWork", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": [{"id": 3201883, "title": "物料配对维护", "hide": false, "need": false, "letter": "matPairDefend", "icon": null, "url": "/matPairDefend", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}, {"id": 3201884, "title": "发票信息管理", "hide": false, "need": false, "letter": "billManage", "icon": null, "url": "/billManage", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}, {"id": 3201885, "title": "地址管理", "hide": false, "need": false, "letter": "addressManage", "icon": null, "url": "/addressManage", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}, {"id": 3201886, "title": "京东待入库清单", "hide": false, "need": false, "letter": "jdWaitInWarehouseList", "icon": null, "url": "/jdWaitInWarehouseList", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}]}, {"id": 10083, "title": "测试一下", "hide": false, "need": false, "letter": "CSYX", "icon": "icon-ji<PERSON><PERSON><PERSON><PERSON>", "url": "/CSYX", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": [{"id": 10253, "title": "多维度查询", "hide": false, "need": false, "letter": "multipleMetaQueryTest", "icon": null, "url": "/multipleMetaQueryTest", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}, {"id": 3202346, "title": "公告", "hide": false, "need": false, "letter": "frame?url=https%3A%2F%2Fssoi-sit.newpearl.com%2F%23%2Fbridge%3FsystemType%3DSYS%26url%3D%252FmsgNoticeList%253Fimmersion%253D1&label=%E5%85%AC%E5%91%8A%E5%88%97%E8%A1%A8", "icon": null, "url": "frame?url=https%3A%2F%2Fssoi-sit.newpearl.com%2F%23%2Fbridge%3FsystemType%3DSYS%26url%3D%252FmsgNoticeList%253Fimmersion%253D1&label=%E5%85%AC%E5%91%8A%E5%88%97%E8%A1%A8", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}, {"id": 3202347, "title": "公告跳转", "hide": false, "need": false, "letter": "https://ssoi-sit.newpearl.com/#/bridge?systemType=SYS&url=/msgNoticeList", "icon": null, "url": "https://ssoi-sit.newpearl.com/#/bridge?systemType=SYS&url=/msgNoticeList", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}, {"id": 3202642, "title": "订单管理", "hide": false, "need": false, "letter": "frame?url=https%3A%2F%2Fssoi-sit.newpearl.com%2F%23%2Fbridge%3FsystemType%3DSYS%26url%3D%252ForderManagementList%253Fimmersion%253D1&label=%E8%AE%A2%E5%8D%95%E7%AE%A1%E7%90%86", "icon": null, "url": "frame?url=https%3A%2F%2Fssoi-sit.newpearl.com%2F%23%2Fbridge%3FsystemType%3DSYS%26url%3D%252ForderManagementList%253Fimmersion%253D1&label=%E8%AE%A2%E5%8D%95%E7%AE%A1%E7%90%86", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}, {"id": 10102, "title": "申购单列表测试", "hide": false, "need": false, "letter": "scmApplyPurchaseListNew", "icon": null, "url": "scmApplyPurchaseListNew", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}, {"id": 10114, "title": "知识", "hide": false, "need": false, "letter": "https://shimo.im", "icon": null, "url": "https://shimo.im", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}, {"id": 10119, "title": "供应商导出报表", "hide": false, "need": false, "letter": "npSupplierReport", "icon": null, "url": "npSupplierReport", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}, {"id": 10223, "title": "申购单详情测试", "hide": false, "need": false, "letter": "scmApplyPurchaseEditNew", "icon": null, "url": "scmApplyPurchaseEditNew", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}]}, {"id": 3203199, "title": "标准管理", "hide": false, "need": false, "letter": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "icon": null, "url": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": [{"id": 3203200, "title": "标准种类", "hide": false, "need": false, "letter": "matStandard/standardType", "icon": null, "url": "matStandard/standardType", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}, {"id": 3203208, "title": "检验项目", "hide": false, "need": false, "letter": "matStandard/detectionProject", "icon": null, "url": "matStandard/detectionProject", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}, {"id": 3203209, "title": "模板管理", "hide": false, "need": false, "letter": "matStandard/template", "icon": null, "url": "matStandard/template", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}, {"id": 3203210, "title": "标准版本管理", "hide": false, "need": false, "letter": "matStandard/standardVersion", "icon": null, "url": "matStandard/standardVersion", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}, {"id": 3203217, "title": "待审批列表", "hide": false, "need": false, "letter": "matStandard/reviewed", "icon": null, "url": "matStandard/reviewed", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}, {"id": 3203218, "title": "标准库", "hide": false, "need": false, "letter": "matStandard/standardBank", "icon": null, "url": "matStandard/standardBank", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}, {"id": 3203219, "title": "权限分配（标准库）", "hide": false, "need": false, "letter": "matStandard/standardBankPower", "icon": null, "url": "matStandard/standardBankPower", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}, {"id": 3203220, "title": "标准与物料对照表", "hide": false, "need": false, "letter": "matStandard/standardMatContrast", "icon": null, "url": "matStandard/standardMatContrast", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}, {"id": 3203221, "title": "标准版本对比", "hide": false, "need": false, "letter": "matStandard/versionContrast", "icon": null, "url": "matStandard/versionContrast", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}]}, {"id": 3203267, "title": "试样管理", "hide": false, "need": false, "letter": "sample", "icon": null, "url": "sample", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": [{"id": 3203288, "title": "预设审批名单", "hide": false, "need": false, "letter": "metaDataReport/sdAssessAccept", "icon": null, "url": "metaDataReport/sdAssessAccept", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}, {"id": 3203268, "title": "试样申请单", "hide": false, "need": false, "letter": "sample/applyOrder", "icon": null, "url": "sample/applyOrder", "oinRel": false, "oinRelCode": null, "orderValue": 1, "children": null}, {"id": 3203273, "title": "试样审批列表", "hide": false, "need": false, "letter": "sample/approveOrder", "icon": null, "url": "sample/approveOrder", "oinRel": false, "oinRelCode": null, "orderValue": 2, "children": null}, {"id": 3203274, "title": "试样评估登记", "hide": false, "need": false, "letter": "sample/applyAssess", "icon": null, "url": "sample/applyAssess", "oinRel": false, "oinRelCode": null, "orderValue": 3, "children": null}, {"id": 3203275, "title": "试样通知列表", "hide": false, "need": false, "letter": "sample/notifyOrder", "icon": null, "url": "sample/notifyOrder", "oinRel": false, "oinRelCode": null, "orderValue": 4, "children": null}, {"id": 3203285, "title": "应用评估申请", "hide": false, "need": false, "letter": "sample/assessApply", "icon": null, "url": "sample/assessApply", "oinRel": false, "oinRelCode": null, "orderValue": 11, "children": null}, {"id": 3203286, "title": "应用标准登记", "hide": false, "need": false, "letter": "sample/assessRegistered", "icon": null, "url": "sample/assessRegistered", "oinRel": false, "oinRelCode": null, "orderValue": 12, "children": null}, {"id": 3203287, "title": "应用评估审核", "hide": false, "need": false, "letter": "sample/assessApprove", "icon": null, "url": "sample/assessApprove", "oinRel": false, "oinRelCode": null, "orderValue": 13, "children": null}, {"id": 3203369, "title": "报表", "hide": false, "need": false, "letter": "sample/report", "icon": null, "url": "sample/report", "oinRel": false, "oinRelCode": null, "orderValue": 14, "children": [{"id": 3203370, "title": "试样申请查询", "hide": false, "need": false, "letter": "sample/report/sampleOrder", "icon": null, "url": "sample/report/sampleOrder", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}, {"id": 3203371, "title": "应用评审申请查询", "hide": false, "need": false, "letter": "sample/report/assessOrder", "icon": null, "url": "sample/report/assessOrder", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}]}]}, {"id": 3201747, "title": "维修管理", "hide": false, "need": false, "letter": "repair", "icon": null, "url": "repair", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": [{"id": 3201748, "title": "维修申请", "hide": false, "need": false, "letter": "repair/application", "icon": null, "url": "repair/application", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": [{"id": 3202575, "title": "退回列表", "hide": false, "need": false, "letter": "repair/application/sendBack", "icon": null, "url": "repair/application/sendBack", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}, {"id": 3201749, "title": "维修申请列表", "hide": false, "need": false, "letter": "repair/application/list", "icon": null, "url": "repair/application/list", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}, {"id": 3201767, "title": "待审批维修列表", "hide": false, "need": false, "letter": "repair/application/reviewed/list", "icon": null, "url": "repair/application/reviewed/list", "oinRel": false, "oinRelCode": null, "orderValue": 1, "children": null}, {"id": 3201770, "title": "待处理维修列表", "hide": false, "need": false, "letter": "repair/application/process/list", "icon": null, "url": "repair/application/process/list", "oinRel": false, "oinRelCode": null, "orderValue": 2, "children": null}, {"id": 3201771, "title": "维修申请明细表", "hide": false, "need": false, "letter": "repair/application/applyDetail/list", "icon": null, "url": "repair/application/applyDetail/list", "oinRel": false, "oinRelCode": null, "orderValue": 3, "children": null}]}, {"id": 3201772, "title": "维修订单", "hide": false, "need": false, "letter": "repair/plan", "icon": null, "url": "repair/plan", "oinRel": false, "oinRelCode": null, "orderValue": 1, "children": [{"id": 3201773, "title": "维修订单列表", "hide": false, "need": false, "letter": "repair/plan/planList/list", "icon": null, "url": "repair/plan/planList/list", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}, {"id": 3201774, "title": "维修订单跟进", "hide": false, "need": false, "letter": "repair/plan/planFollow/list", "icon": null, "url": "repair/plan/planFollow/list", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}]}, {"id": 3201775, "title": "维修报价", "hide": false, "need": false, "letter": "repair/offer", "icon": null, "url": "repair/offer", "oinRel": false, "oinRelCode": null, "orderValue": 2, "children": [{"id": 3202360, "title": "维修报价审核人设置", "hide": false, "need": false, "letter": "repair/offer/repairOfferAuditSetting", "icon": null, "url": "repair/offer/repairOfferAuditSetting", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}, {"id": 3202391, "title": "维修报价", "hide": false, "need": false, "letter": "repair/B2BSystem/quote/quoteList", "icon": null, "url": "repair/B2BSystem/quote/quoteList", "oinRel": false, "oinRelCode": null, "orderValue": 1, "children": null}, {"id": 3201776, "title": "维修报价审核", "hide": false, "need": false, "letter": "repair/offer/review/list", "icon": null, "url": "repair/offer/review/list", "oinRel": false, "oinRelCode": null, "orderValue": 2, "children": null}, {"id": 3202361, "title": "历史维修报价明细报表", "hide": false, "need": false, "letter": "repairOfferDetailHistory", "icon": null, "url": "repairOfferDetailHistory", "oinRel": false, "oinRelCode": null, "orderValue": 4, "children": null}]}, {"id": 3201764, "title": "到货通知", "hide": false, "need": false, "letter": "repair/arrivalBill", "icon": null, "url": "repair/arrivalBill", "oinRel": false, "oinRelCode": null, "orderValue": 3, "children": [{"id": 3202392, "title": "维修发货单", "hide": false, "need": false, "letter": "repair/arrivalBill/dispatchBill", "icon": null, "url": "repair/arrivalBill/dispatchBill", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": [{"id": 3202393, "title": "待维修清单", "hide": false, "need": false, "letter": "repair/arrivalBill/dispatchBill/toRepair", "icon": null, "url": "repair/arrivalBill/dispatchBill/toRepair", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}, {"id": 3202394, "title": "维修发货单", "hide": false, "need": false, "letter": "repair/arrivalBill/dispatchBill/repairDeliverOrder", "icon": null, "url": "repair/arrivalBill/dispatchBill/repairDeliverOrder", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}, {"id": 3202395, "title": "维修送货明细查询", "hide": false, "need": false, "letter": "repair/arrivalBill/dispatchBill/repairDeliverDetail", "icon": null, "url": "repair/arrivalBill/dispatchBill/repairDeliverDetail", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}]}, {"id": 3203178, "title": "报表查询", "hide": false, "need": false, "letter": ".", "icon": null, "url": ".", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": [{"id": 3203179, "title": "维修报价查询报表", "hide": false, "need": false, "letter": "repair/arrivalBill/report/repairOfferSearch", "icon": null, "url": "repair/arrivalBill/report/repairOfferSearch", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}, {"id": 3203180, "title": "维修报价明细查询报表", "hide": false, "need": false, "letter": "repair/arrivalBill/report/repairOfferDetailSearch", "icon": null, "url": "repair/arrivalBill/report/repairOfferDetailSearch", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}]}, {"id": 3203190, "title": "到货验收清单查询", "hide": false, "need": false, "letter": "repair/arrivalBill/repairReceiveOrderList", "icon": null, "url": "repair/arrivalBill/repairReceiveOrderList", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}, {"id": 3201777, "title": "到货通知单", "hide": false, "need": false, "letter": "repair/arrivalBill/arrivalNotice/list", "icon": null, "url": "repair/arrivalBill/arrivalNotice/list", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}, {"id": 3201765, "title": "到货验收列表", "hide": false, "need": false, "letter": "repair/arrivalBill/arrivalCheck", "icon": null, "url": "repair/arrivalBill/arrivalCheck", "oinRel": false, "oinRelCode": null, "orderValue": 1, "children": null}]}]}, {"id": 3200225, "title": "计划管理", "hide": false, "need": false, "letter": "plan", "icon": "icon-shuju<PERSON><PERSON><PERSON>", "url": "/plan", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": [{"id": 3200226, "title": "计划单", "hide": false, "need": false, "letter": "scmDurationPlan", "icon": null, "url": "/scmDurationPlan", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}, {"id": 3200244, "title": "计划处理", "hide": false, "need": false, "letter": "scmDurationPlanHandle", "icon": null, "url": "/scmDurationPlanHandle", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}]}, {"id": 10017, "title": "采购管理", "hide": false, "need": false, "letter": "1", "icon": "icon-ca<PERSON>ug<PERSON><PERSON>", "url": "1", "oinRel": false, "oinRelCode": null, "orderValue": 1, "children": [{"id": 10018, "title": "合同管理", "hide": false, "need": false, "letter": "2", "icon": null, "url": "2", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": [{"id": 10019, "title": "采购合同", "hide": false, "need": false, "letter": "scmPurchaseContractH", "icon": null, "url": "scmPurchaseContractH", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}, {"id": 10021, "title": "合同变更审批", "hide": false, "need": false, "letter": "scmPurchaseContractD", "icon": null, "url": "scmPurchaseContractD", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}, {"id": 10022, "title": "报表", "hide": false, "need": false, "letter": "5", "icon": null, "url": "5", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": [{"id": 10023, "title": "合同汇总查询", "hide": false, "need": false, "letter": "scmPurchaseContractHReport", "icon": null, "url": "scmPurchaseContractHReport", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}, {"id": 10024, "title": "合同明细查询", "hide": false, "need": false, "letter": "scmPurchaseContractHReportDetail", "icon": null, "url": "scmPurchaseContractHReportDetail", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}, {"id": 3202604, "title": "物料月度单价明细表", "hide": false, "need": false, "letter": "contract/unitPriceReportForm", "icon": null, "url": "contract/unitPriceReportForm", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}, {"id": 3203160, "title": "合同物料最低价查询", "hide": false, "need": false, "letter": "contract/contractMatMinPrice", "icon": null, "url": "contract/contractMatMinPrice", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}, {"id": 3203161, "title": "合同物料同比查询", "hide": false, "need": false, "letter": "contract/contractMatAllPrice", "icon": null, "url": "contract/contractMatAllPrice", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}, {"id": 3200224, "title": "纸箱明细单价", "hide": false, "need": false, "letter": "scmCartonDetailPrice", "icon": null, "url": "scmCartonDetailPrice", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}, {"id": 3200230, "title": "纸箱明细单价变更对比", "hide": false, "need": false, "letter": "scmCartonDetailPriceChangeComparison", "icon": null, "url": "scmCartonDetailPriceChangeComparison", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}, {"id": 10040, "title": "合同版本对比", "hide": false, "need": false, "letter": "scmPurchaseContractHReportCompare", "icon": null, "url": "scmPurchaseContractHReportCompare", "oinRel": false, "oinRelCode": null, "orderValue": 8, "children": null}, {"id": 10053, "title": "合同变更明细查询", "hide": false, "need": false, "letter": "scmPurchaseContractHReportChangeDetail", "icon": null, "url": "scmPurchaseContractHReportChangeDetail", "oinRel": false, "oinRelCode": null, "orderValue": 9, "children": null}, {"id": 10054, "title": "合同补录明细查询", "hide": false, "need": false, "letter": "scmPurchaseContractHReportInputDetail", "icon": null, "url": "scmPurchaseContractHReportInputDetail", "oinRel": false, "oinRelCode": null, "orderValue": 10, "children": null}]}, {"id": 3203425, "title": "合同清单", "hide": false, "need": false, "letter": "contractList", "icon": null, "url": "contractList", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}, {"id": 3203426, "title": "合同跟进", "hide": false, "need": false, "letter": "contractFollow", "icon": null, "url": "contractFollow", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": [{"id": 3203427, "title": "材料进场验收", "hide": false, "need": false, "letter": "matEntryAccept", "icon": null, "url": "matEntryAccept", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}, {"id": 3203428, "title": "交付使用", "hide": false, "need": false, "letter": "deliverUser", "icon": null, "url": "deliverUser", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}, {"id": 3203429, "title": "完工评价", "hide": false, "need": false, "letter": "completeEvaluation", "icon": null, "url": "completeEvaluation", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}, {"id": 3203430, "title": "最终验收", "hide": false, "need": false, "letter": "finalAccept", "icon": null, "url": "finalAccept", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}, {"id": 3203431, "title": "保质期终止确认", "hide": false, "need": false, "letter": "qualityEndSure", "icon": null, "url": "qualityEndSure", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}]}]}, {"id": 3203106, "title": "开票管理", "hide": false, "need": false, "letter": "invoice", "icon": null, "url": "/invoice", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": [{"id": 3203107, "title": "发票安排", "hide": false, "need": false, "letter": "invoiceArrange", "icon": null, "url": "/invoiceArrange", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}, {"id": 3203116, "title": "开票查询报表", "hide": false, "need": false, "letter": "scmInvoiceReport", "icon": null, "url": "/scmInvoiceReport", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}]}, {"id": 10044, "title": "申购单", "hide": false, "need": false, "letter": "scmApplyPurchase", "icon": null, "url": "scmApplyPurchase", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": [{"id": 3201603, "title": "待审批申购列表", "hide": false, "need": false, "letter": "scmAddApprovalPendingApplyPurchaseList", "icon": null, "url": "scmAddApprovalPendingApplyPurchaseList", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}, {"id": 10115, "title": "采购计划查询", "hide": false, "need": false, "letter": "scmApplyFollow", "icon": "icon-ca<PERSON>ug<PERSON><PERSON>", "url": "scmApplyFollow", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}, {"id": 3201726, "title": "待处理请求(新)", "hide": false, "need": false, "letter": "scmPendrequestsNew", "icon": null, "url": "scmPendrequestsNew", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}, {"id": 3201246, "title": "申购跟踪查询", "hide": false, "need": false, "letter": "orderTrack", "icon": null, "url": "orderTrack", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}, {"id": 10045, "title": "申购单列表", "hide": false, "need": false, "letter": "scmApplyPurchaseList", "icon": null, "url": "scmApplyPurchaseList", "oinRel": false, "oinRelCode": null, "orderValue": 5, "children": null}, {"id": 10046, "title": "待处理请求", "hide": false, "need": false, "letter": "scmPendrequests", "icon": null, "url": "scmPendrequests", "oinRel": false, "oinRelCode": null, "orderValue": 6, "children": null}]}, {"id": 10059, "title": "采购管理", "hide": false, "need": false, "letter": "1", "icon": null, "url": "1", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": [{"id": 10060, "title": "采购订单", "hide": false, "need": false, "letter": "scmPoH", "icon": null, "url": "scmPoH", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}, {"id": 10061, "title": "订单跟进", "hide": false, "need": false, "letter": "scmPoD", "icon": null, "url": "scmPoD", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}]}, {"id": 10130, "title": "询价管理", "hide": false, "need": false, "letter": "4", "icon": null, "url": "4", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": [{"id": 10131, "title": "询价处理", "hide": false, "need": false, "letter": "scmB2BQuotationHandle", "icon": null, "url": "scmB2BQuotationHandle", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}, {"id": 3201271, "title": "询价审批-主管", "hide": false, "need": false, "letter": "inquiryExamine", "icon": null, "url": "inquiryExamine", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}, {"id": 10132, "title": "比价处理", "hide": false, "need": false, "letter": "inquiryBillHandle", "icon": null, "url": "inquiryBillHandle", "oinRel": false, "oinRelCode": null, "orderValue": 1, "children": null}]}, {"id": 10141, "title": "申购单详情", "hide": false, "need": false, "letter": "shengoudangxiangqing", "icon": null, "url": "shengoudangxiangqing", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}]}, {"id": 10193, "title": "招投标管理", "hide": false, "need": false, "letter": "bidding", "icon": "icon-ca<PERSON>ug<PERSON><PERSON>", "url": "bidding", "oinRel": false, "oinRelCode": null, "orderValue": 2, "children": [{"id": 10196, "title": "招标大厅", "hide": false, "need": false, "letter": "bidHall", "icon": null, "url": "bidHall", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}, {"id": 10202, "title": "招标发布", "hide": false, "need": false, "letter": "bidTenderExamine", "icon": null, "url": "bidTenderExamine", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}, {"id": 10194, "title": "招投标基础设置", "hide": false, "need": false, "letter": "bidding_setting", "icon": null, "url": "bidding_setting", "oinRel": false, "oinRelCode": null, "orderValue": 1, "children": [{"id": 3203381, "title": "权限分配(线部领导)", "hide": false, "need": false, "letter": "bidRightAssign", "icon": null, "url": "bidRightAssign", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}, {"id": 10195, "title": "评分组员管理", "hide": false, "need": false, "letter": "bidScoringMemberLib", "icon": null, "url": "bidScoringMemberLib", "oinRel": false, "oinRelCode": null, "orderValue": 1, "children": null}, {"id": 10201, "title": "招标评分模板库", "hide": false, "need": false, "letter": "bidScoringTemplateLib", "icon": null, "url": "bidScoringTemplateLib", "oinRel": false, "oinRelCode": null, "orderValue": 2, "children": null}]}, {"id": 3203382, "title": "招标中心", "hide": false, "need": false, "letter": "bidTenderCenter", "icon": null, "url": "bidTenderCenter", "oinRel": false, "oinRelCode": null, "orderValue": 2, "children": null}, {"id": 3203383, "title": "招标大厅(新)", "hide": false, "need": false, "letter": "bidHallNew", "icon": null, "url": "bidHallNew", "oinRel": false, "oinRelCode": null, "orderValue": 2, "children": null}, {"id": 3201354, "title": "招标审核", "hide": false, "need": false, "letter": "bidToBeAudit", "icon": null, "url": "bidToBeAudit", "oinRel": false, "oinRelCode": null, "orderValue": 2, "children": null}, {"id": 10203, "title": "开标中心", "hide": false, "need": false, "letter": "bidOpenCenter", "icon": null, "url": "bidOpenCenter", "oinRel": false, "oinRelCode": null, "orderValue": 4, "children": null}, {"id": 10204, "title": "评标中心", "hide": false, "need": false, "letter": "bidEvaluationCenter", "icon": null, "url": "bidEvaluationCenter", "oinRel": false, "oinRelCode": null, "orderValue": 6, "children": null}, {"id": 10205, "title": "定标中心", "hide": false, "need": false, "letter": "pendingTenderCenter", "icon": null, "url": "pendingTenderCenter", "oinRel": false, "oinRelCode": null, "orderValue": 7, "children": null}]}, {"id": 10197, "title": "库存管理", "hide": false, "need": false, "letter": "wms", "icon": "icon-shuju<PERSON><PERSON><PERSON>", "url": "/wms", "oinRel": false, "oinRelCode": null, "orderValue": 3, "children": [{"id": 3203298, "title": "物料使用跟踪", "hide": false, "need": false, "letter": "t3es", "icon": null, "url": null, "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": [{"id": 3203299, "title": "领料使用跟踪", "hide": false, "need": false, "letter": "wms/receive/waiting", "icon": null, "url": "wms/receive/waiting", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}, {"id": 3203300, "title": "领料使用单", "hide": false, "need": false, "letter": "wms/receive/using", "icon": null, "url": "wms/receive/using", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}]}, {"id": 10251, "title": "购物申请单", "hide": false, "need": false, "letter": "wms/requestBill", "icon": null, "url": "wms/requestBill", "oinRel": false, "oinRelCode": null, "orderValue": 1, "children": [{"id": 10252, "title": "购物申请单", "hide": false, "need": false, "letter": "wms/requestBill/purchase/list", "icon": null, "url": "wms/requestBill/purchase/list", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}, {"id": 3203097, "title": "审批配置", "hide": false, "need": false, "letter": "requestBill/approveConfig", "icon": null, "url": "requestBill/approveConfig", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": [{"id": 3203098, "title": "项目物料分类", "hide": false, "need": false, "letter": "wms/requestBill/approveConfig/projectMat", "icon": null, "url": "wms/requestBill/approveConfig/projectMat", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}, {"id": 3203110, "title": "审批流程设置", "hide": false, "need": false, "letter": "wms/requestBill/approveConfig/approveFlow", "icon": null, "url": "wms/requestBill/approveConfig/approveFlow", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}, {"id": 3203111, "title": "工厂部门关系维护", "hide": false, "need": false, "letter": "wms/requestBill/approveConfig/category", "icon": null, "url": "wms/requestBill/approveConfig/category", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}, {"id": 3203155, "title": "业务类型控制", "hide": false, "need": false, "letter": "wms/requestBill/approveConfig/bizTypeCont", "icon": null, "url": "wms/requestBill/approveConfig/bizTypeCont", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}]}, {"id": 3203140, "title": "购物申请审批", "hide": false, "need": false, "letter": "wms/requestBill/purchase/approveBatch", "icon": null, "url": "wms/requestBill/purchase/approveBatch", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}, {"id": 3201870, "title": "购物申请审批查询", "hide": false, "need": false, "letter": "wms/requestBill/purchase/approveSearch", "icon": null, "url": "wms/requestBill/purchase/approveSearch", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}, {"id": 3201209, "title": "购物申请单情况查询报表", "hide": false, "need": false, "letter": "wms/requestBill/purchase/form", "icon": null, "url": "wms/requestBill/purchase/form", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}, {"id": 3201210, "title": "购物申请单(仓库)", "hide": false, "need": false, "letter": "wms/requestBill/purchase/list/approve", "icon": null, "url": "wms/requestBill/purchase/list/approve", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}, {"id": 3201259, "title": "申购单申请查询", "hide": false, "need": false, "letter": "metaDataReport/PurchaseApplication", "icon": null, "url": "metaDataReport/PurchaseApplication", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}]}, {"id": 3200222, "title": "领料申请单", "hide": false, "need": false, "letter": "wms/comsume", "icon": null, "url": "wms/comsume", "oinRel": false, "oinRelCode": null, "orderValue": 2, "children": [{"id": 3203139, "title": "退货申请情况查询报表", "hide": false, "need": false, "letter": "wms/requestBill/consume/formReturn", "icon": null, "url": "wms/requestBill/consume/formReturn", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}, {"id": 3203142, "title": "领料申请审批", "hide": false, "need": false, "letter": "wms/requestBill/consume/approveBatch", "icon": null, "url": "wms/requestBill/consume/approveBatch", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}, {"id": 3201871, "title": "领料单审批查询", "hide": false, "need": false, "letter": "wms/requestBill/consume/approveSearch", "icon": null, "url": "wms/requestBill/consume/approveSearch", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}, {"id": 3200227, "title": "领料申请单", "hide": false, "need": false, "letter": "wms/requestBill/consume/list", "icon": null, "url": "wms/requestBill/consume/list", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}, {"id": 3200228, "title": "领料申请情况查询报表", "hide": false, "need": false, "letter": "wms/requestBill/consume/form", "icon": null, "url": "wms/requestBill/consume/form", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}, {"id": 3200229, "title": "领料申请单(仓库)", "hide": false, "need": false, "letter": "wms/requestBill/consume/approve", "icon": null, "url": "wms/requestBill/consume/approve", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}, {"id": 3200249, "title": "领用物料审核设置", "hide": false, "need": false, "letter": "wms/requestBill/consume/settings", "icon": null, "url": "wms/requestBill/consume/settings", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}]}, {"id": 10218, "title": "入库管理", "hide": false, "need": false, "letter": "wms/inbound", "icon": null, "url": "wms/inbound", "oinRel": false, "oinRelCode": null, "orderValue": 3, "children": [{"id": 3202397, "title": "成品生产入库（作废本期不做）", "hide": false, "need": false, "letter": "wms/inbound/finishedProduct", "icon": null, "url": "wms/inbound/finishedProduct", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}, {"id": 10231, "title": "材料入库单", "hide": false, "need": false, "letter": "wms/inbound/list?billType=ReceivingWarehousing", "icon": null, "url": "wms/inbound/list?billType=ReceivingWarehousing", "oinRel": false, "oinRelCode": null, "orderValue": 1, "children": null}, {"id": 10233, "title": "材料入库单(寄仓)", "hide": false, "need": false, "letter": "wms/inbound/list?billType=DepositReceivingWarehouse", "icon": null, "url": "wms/inbound/list?billType=DepositReceivingWarehouse", "oinRel": false, "oinRelCode": null, "orderValue": 2, "children": null}, {"id": 3202609, "title": "原材料其它入库单", "hide": false, "need": false, "letter": "wms/inbound/coal/list", "icon": null, "url": "wms/inbound/coal/list", "oinRel": false, "oinRelCode": null, "orderValue": 3, "children": null}]}, {"id": 10219, "title": "出库管理", "hide": false, "need": false, "letter": "wms/outbount", "icon": null, "url": "wms/outbount", "oinRel": false, "oinRelCode": null, "orderValue": 4, "children": [{"id": 10241, "title": "领用出库单", "hide": false, "need": false, "letter": "wms/outbound/apply/list", "icon": null, "url": "wms/outbound/apply/list", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}, {"id": 3202630, "title": "供应商扣款单", "hide": false, "need": false, "letter": "wms/supplier/deduct", "icon": null, "url": "wms/supplier/deduct", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}, {"id": 3202404, "title": "领料出库清单", "hide": false, "need": false, "letter": "wms/outbound/coal/list", "icon": null, "url": "wms/outbound/coal/list", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}, {"id": 3202405, "title": "销售出库单", "hide": false, "need": false, "letter": "wms/outbound/sale/list", "icon": null, "url": "wms/outbound/sale/list", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}]}, {"id": 3202398, "title": "调拨管理", "hide": false, "need": false, "letter": "/wms/allot", "icon": null, "url": "/wms/allot", "oinRel": false, "oinRelCode": null, "orderValue": 5, "children": [{"id": 3202399, "title": "调拨订单列表", "hide": false, "need": false, "letter": "wms/allot/list", "icon": null, "url": "wms/allot/list", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}, {"id": 3202402, "title": "调拨出库单列表", "hide": false, "need": false, "letter": "wms/allot/transferOutOrderList", "icon": null, "url": "wms/allot/transferOutOrderList", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}, {"id": 3202403, "title": "调拨入库单列表", "hide": false, "need": false, "letter": "wms/allot/transferInOrderList", "icon": null, "url": "wms/allot/transferInOrderList", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}]}, {"id": 10242, "title": "事后修改", "hide": false, "need": false, "letter": "/wms/afterwardsModify", "icon": null, "url": "/wms/afterwardsModify", "oinRel": false, "oinRelCode": null, "orderValue": 6, "children": [{"id": 10243, "title": "仓库业务事后修改单", "hide": false, "need": false, "letter": "wms/afterwardsModify/depotBusiness", "icon": null, "url": "wms/afterwardsModify/depotBusiness", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}, {"id": 10244, "title": "业务单据检查放行单", "hide": false, "need": false, "letter": "wms/afterwardsModify/releaseBusiness", "icon": null, "url": "wms/afterwardsModify/releaseBusiness", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}]}, {"id": 10248, "title": "发外维修", "hide": false, "need": false, "letter": "/wms/sendOutMaintain", "icon": null, "url": "/wms/sendOutMaintain", "oinRel": false, "oinRelCode": null, "orderValue": 7, "children": [{"id": 10249, "title": "发外维修出库单", "hide": false, "need": false, "letter": "wms/sendOutMaintain/sendOutMaintainLeave", "icon": null, "url": "wms/sendOutMaintain/sendOutMaintainLeave", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}, {"id": 10250, "title": "发外维修入库单", "hide": false, "need": false, "letter": "wms/sendOutMaintain/sendOutMaintainEnter", "icon": null, "url": "wms/sendOutMaintain/sendOutMaintainEnter", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}]}, {"id": 10216, "title": "库存调整", "hide": false, "need": false, "letter": "/wms/storagEadjust", "icon": null, "url": "/wms/storagEadjust", "oinRel": false, "oinRelCode": null, "orderValue": 8, "children": [{"id": 3203156, "title": "供应商扣款单", "hide": false, "need": false, "letter": "wms/supplier/deduct", "icon": null, "url": "wms/supplier/deduct", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}, {"id": 10217, "title": "库位调整单", "hide": false, "need": false, "letter": "wms/storagEadjust/storagEadjustBill", "icon": null, "url": "wms/storagEadjust/storagEadjustBill", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}, {"id": 10221, "title": "材料属性转换", "hide": false, "need": false, "letter": "wms/storagEadjust/matAttTransform", "icon": null, "url": "wms/storagEadjust/matAttTransform", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}, {"id": 10224, "title": "材料调价单", "hide": false, "need": false, "letter": "wms/storagEadjust/stuffEadjustBill", "icon": null, "url": "wms/storagEadjust/stuffEadjustBill", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}, {"id": 10235, "title": "金额出仓单", "hide": false, "need": false, "letter": "wms/storagEadjust/amountOutBill", "icon": null, "url": "wms/storagEadjust/amountOutBill", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}]}, {"id": 3200266, "title": "工具管理", "hide": false, "need": false, "letter": "/wms/tool", "icon": null, "url": "/wms/tool", "oinRel": false, "oinRelCode": null, "orderValue": 9, "children": [{"id": 3200267, "title": "工具借出单", "hide": false, "need": false, "letter": "wms/tool/toolsLendBill", "icon": null, "url": "wms/tool/toolsLendBill", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}, {"id": 3200268, "title": "工具归还单", "hide": false, "need": false, "letter": "wms/tool/toolsReturnBill", "icon": null, "url": "wms/tool/toolsReturnBill", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}, {"id": 3202330, "title": "工具查询报表", "hide": false, "need": false, "letter": "wms/tool/toolsSearchNew", "icon": null, "url": "wms/tool/toolsSearchNew", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}, {"id": 3200269, "title": "工具管理查询", "hide": false, "need": false, "letter": "wms/tool/toolsSearch", "icon": null, "url": "wms/tool/toolsSearch", "oinRel": false, "oinRelCode": null, "orderValue": 1, "children": null}]}, {"id": 3200237, "title": "报表", "hide": false, "need": false, "letter": "/wms/report", "icon": null, "url": "/wms/report", "oinRel": false, "oinRelCode": null, "orderValue": 10, "children": [{"id": 3200256, "title": "材料日发生数查询", "hide": false, "need": false, "letter": "wms/report/matStockDaily", "icon": null, "url": "wms/report/matStockDaily", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}, {"id": 3200257, "title": "材料仓月报表查询", "hide": false, "need": false, "letter": "wms/report/stockReportMonthly", "icon": null, "url": "wms/report/stockReportMonthly", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}, {"id": 3200258, "title": "材料仓库存查询", "hide": false, "need": false, "letter": "wms/report/matDepotReport", "icon": null, "url": "wms/report/matDepotReport", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}, {"id": 3203085, "title": "调拨订单查询", "hide": false, "need": false, "letter": "wms/report/transferOrder", "icon": null, "url": "wms/report/transferOrder", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}, {"id": 3203115, "title": "2183预算单查询", "hide": false, "need": false, "letter": "budgetQuery", "icon": null, "url": "/budgetQuery", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}, {"id": 3203119, "title": "报表查询（T-1）", "hide": false, "need": false, "letter": "reportTOne", "icon": null, "url": "reportTOne", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": [{"id": 3203120, "title": "材料仓月报表查询（多选）", "hide": false, "need": false, "letter": "wms/report/stockReportMonthlyTOne", "icon": null, "url": "wms/report/stockReportMonthlyTOne", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}, {"id": 3203121, "title": "供应汇总表（T-1）", "hide": false, "need": false, "letter": "wms/report/supCollectTOne", "icon": null, "url": "wms/report/supCollectTOne", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}, {"id": 3203122, "title": "领用汇总表（T-1）", "hide": false, "need": false, "letter": "wms/report/consumingTOne", "icon": null, "url": "wms/report/consumingTOne", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}]}, {"id": 3201588, "title": "发外维修汇总查询报表", "hide": false, "need": false, "letter": "wms/report/sendOutMaintain", "icon": null, "url": "wms/report/sendOutMaintain", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}, {"id": 3202632, "title": "销售磅码单查询报表", "hide": false, "need": false, "letter": "wms/report/salesWeight", "icon": null, "url": "wms/report/salesWeight", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}, {"id": 3201665, "title": "材料分类明细", "hide": false, "need": false, "letter": "wms/report/matGroupDetail", "icon": null, "url": "wms/report/matGroupDetail", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}, {"id": 3201723, "title": "供应汇总表", "hide": false, "need": false, "letter": "wms/report/supCollect", "icon": null, "url": "wms/report/supCollect", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}, {"id": 3201724, "title": "领用汇总表", "hide": false, "need": false, "letter": "wms/report/consuming", "icon": null, "url": "wms/report/consuming", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}, {"id": 3201238, "title": "材料业务查询", "hide": false, "need": false, "letter": "wms/report/stockLedgerLog", "icon": null, "url": "wms/report/stockLedgerLog", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}, {"id": 3201252, "title": "过磅查询报表", "hide": false, "need": false, "letter": "wms/report/weigh", "icon": null, "url": "wms/report/weigh", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}, {"id": 3201256, "title": "订单所需物料库存情况", "hide": false, "need": false, "letter": "wms/report/materialInventoryByPlan", "icon": null, "url": "wms/report/materialInventoryByPlan", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}, {"id": 3200238, "title": "寄仓物料出仓未达应付账", "hide": false, "need": false, "letter": "wms/report/depositMatLeave", "icon": null, "url": "wms/report/depositMatLeave", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}, {"id": 3201264, "title": "供应商寄仓产品", "hide": false, "need": false, "letter": "wms/report/warehourse", "icon": null, "url": "wms/report/warehourse", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}, {"id": 3200254, "title": "材料库位库存查询", "hide": false, "need": false, "letter": "wms/report/matLocation", "icon": null, "url": "wms/report/matLocation", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}, {"id": 3200255, "title": "呆滞物资查询", "hide": false, "need": false, "letter": "wms/report/retentionMat", "icon": null, "url": "wms/report/retentionMat", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}]}]}, {"id": 10091, "title": "报表管理", "hide": false, "need": false, "letter": "3", "icon": "icon-shuju<PERSON><PERSON><PERSON>", "url": "3", "oinRel": false, "oinRelCode": null, "orderValue": 4, "children": [{"id": 3200260, "title": "供应商投标竞价汇总表", "hide": false, "need": false, "letter": "supTenderSumReport", "icon": null, "url": "supTenderSumReport", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}, {"id": 3200261, "title": "投标供应商资料登记情况表", "hide": false, "need": false, "letter": "supTenderRegistReport", "icon": null, "url": "supTenderRegistReport", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}, {"id": 3201803, "title": "供应结算汇总", "hide": false, "need": false, "letter": "wms/report/supAnnualSettleSummary", "icon": null, "url": "wms/report/supAnnualSettleSummary", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}, {"id": 3201804, "title": "图表报表", "hide": false, "need": false, "letter": "chart", "icon": null, "url": "chart", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": [{"id": 3201805, "title": "物料价格趋势表", "hide": false, "need": false, "letter": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "icon": null, "url": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}, {"id": 3202628, "title": "数据看板(个人)", "hide": false, "need": false, "letter": "databoard?queryType=personal", "icon": null, "url": "databoard?queryType=personal", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}, {"id": 3202629, "title": "数据看板(主管)", "hide": false, "need": false, "letter": "databoard?queryType=manager", "icon": null, "url": "databoard?queryType=manager", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}]}, {"id": 3200286, "title": "物料主档处理报表", "hide": false, "need": false, "letter": "matDealBillReport", "icon": null, "url": "matDealBillReport", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}, {"id": 3203112, "title": "月度供货报表", "hide": false, "need": false, "letter": "report/monthSupply", "icon": null, "url": "report/monthSupply", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}, {"id": 3202358, "title": "供应商供货情况明细表-按物料大类", "hide": false, "need": false, "letter": "report/supSituationMatGroup", "icon": null, "url": "report/supSituationMatGroup", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}, {"id": 3202366, "title": "供应商供货情况明细表-按物料编码", "hide": false, "need": false, "letter": "report/supSituationMatDetail", "icon": null, "url": "report/supSituationMatDetail", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}, {"id": 10092, "title": "物料编码申请明细报表", "hide": false, "need": false, "letter": "scmMaterielCodeApply/Detail", "icon": null, "url": "scmMaterielCodeApply/Detail", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}, {"id": 10101, "title": "物料查询报表", "hide": false, "need": false, "letter": "materielMain/search", "icon": null, "url": "materielMain/search", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}, {"id": 10117, "title": "报价清单报表", "hide": false, "need": false, "letter": "metaDataReport/scmB2BQuotationReport", "icon": null, "url": "metaDataReport/scmB2BQuotationReport", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}, {"id": 3201727, "title": "供应商结算账户审核查询", "hide": false, "need": false, "letter": "npSupplierBankBillReport", "icon": null, "url": "npSupplierBankBillReport", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}, {"id": 3201216, "title": "未处理申请明细统计报表", "hide": false, "need": false, "letter": "purchaseOrder", "icon": null, "url": "purchaseOrder", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}, {"id": 3201728, "title": "供应商账户查询", "hide": false, "need": false, "letter": "supplierBankAccountQuery", "icon": null, "url": "supplierBankAccountQuery", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}, {"id": 3201217, "title": "维涛历史包装物合同报表", "hide": false, "need": false, "letter": "historyContract", "icon": null, "url": "historyContract", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}, {"id": 3201740, "title": "物料编码申请明细报表(新)", "hide": false, "need": false, "letter": "scmMaterielCodeApply/report", "icon": null, "url": "scmMaterielCodeApply/report", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}, {"id": 3200218, "title": "统计报表", "hide": false, "need": false, "letter": "12", "icon": null, "url": "12", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": [{"id": 3200219, "title": "B2B订单统计", "hide": false, "need": false, "letter": "metaDataReport/supB2BOrderStatisticsReport", "icon": null, "url": "metaDataReport/supB2BOrderStatisticsReport", "oinRel": false, "oinRelCode": null, "orderValue": 2, "children": null}, {"id": 3200223, "title": "发货单统计报表", "hide": false, "need": false, "letter": "metaDataReport/supB2BInvoiceReport", "icon": null, "url": "metaDataReport/supB2BInvoiceReport", "oinRel": false, "oinRelCode": null, "orderValue": 7, "children": null}]}, {"id": 3201766, "title": "供应商B2B权限查询", "hide": false, "need": false, "letter": "b2bPrivileges", "icon": null, "url": "b2bPrivileges", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}, {"id": 10123, "title": "合同明细查询", "hide": false, "need": false, "letter": "metaDataReport/scmPurchaseContractDReport", "icon": null, "url": "metaDataReport/scmPurchaseContractDReport", "oinRel": false, "oinRelCode": null, "orderValue": 1, "children": null}, {"id": 10100, "title": "未入库订单明细查询报表", "hide": false, "need": false, "letter": "metaDataReport/scmNotInPoReport", "icon": null, "url": "metaDataReport/scmNotInPoReport", "oinRel": false, "oinRelCode": null, "orderValue": 2, "children": null}, {"id": 3201211, "title": "非寄仓订单与合同单价差异报表", "hide": false, "need": false, "letter": "metaDataReport/ContractPriceVarianceReport", "icon": null, "url": "metaDataReport/ContractPriceVarianceReport", "oinRel": false, "oinRelCode": null, "orderValue": 7, "children": null}, {"id": 10212, "title": "B2B供应商上线跟踪表", "hide": false, "need": false, "letter": "metaDataReport/AsupB2BOnlineFollowUpReport", "icon": null, "url": "metaDataReport/AsupB2BOnlineFollowUpReport", "oinRel": false, "oinRelCode": null, "orderValue": 7, "children": null}]}, {"id": 10037, "title": "B2B系统", "hide": false, "need": false, "letter": "2", "icon": "icon-B2Bxitong", "url": "2", "oinRel": false, "oinRelCode": null, "orderValue": 5, "children": [{"id": 10047, "title": "档案维护", "hide": false, "need": false, "letter": "B2BFile", "icon": null, "url": "B2BFile", "oinRel": false, "oinRelCode": null, "orderValue": 1, "children": [{"id": 10048, "title": "维护发货车辆", "hide": false, "need": false, "letter": "scmB2BCarFile", "icon": null, "url": "scmB2BCarFile", "oinRel": false, "oinRelCode": null, "orderValue": 1, "children": null}]}, {"id": 10103, "title": "询报价管理", "hide": false, "need": false, "letter": "2", "icon": null, "url": "2", "oinRel": false, "oinRelCode": null, "orderValue": 1, "children": [{"id": 3203401, "title": "报价大厅(新)", "hide": false, "need": false, "letter": "enquiry/quotationHall", "icon": null, "url": "enquiry/quotationHall", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}, {"id": 3203418, "title": "报价查询", "hide": false, "need": false, "letter": "scmQuotationSearch", "icon": null, "url": "scmQuotationSearch", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}, {"id": 10104, "title": "报价大厅", "hide": false, "need": false, "letter": "scmB2BQuotationList", "icon": null, "url": "scmB2BQuotationList", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}, {"id": 10107, "title": "无合同订单", "hide": false, "need": false, "letter": "scmB2BPendrequests", "icon": null, "url": "scmB2BPendrequests", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}]}, {"id": 10206, "title": "招投标管理", "hide": false, "need": false, "letter": "bid", "icon": null, "url": "bid", "oinRel": false, "oinRelCode": null, "orderValue": 2, "children": [{"id": 3203384, "title": "投标大厅(新)", "hide": false, "need": false, "letter": "tenderHallNew", "icon": null, "url": "tenderHallNew", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}, {"id": 10207, "title": "投标大厅", "hide": false, "need": false, "letter": "tenderHall", "icon": null, "url": "tenderHall", "oinRel": false, "oinRelCode": null, "orderValue": 1, "children": null}, {"id": 10208, "title": "拍卖大厅", "hide": false, "need": false, "letter": "auctionHall", "icon": null, "url": "auctionHall", "oinRel": false, "oinRelCode": null, "orderValue": 2, "children": null}, {"id": 10215, "title": "投标查询", "hide": false, "need": false, "letter": "bid<PERSON><PERSON>y", "icon": null, "url": "bid<PERSON><PERSON>y", "oinRel": false, "oinRelCode": null, "orderValue": 3, "children": null}]}, {"id": 3202292, "title": "维修管理", "hide": false, "need": false, "letter": "repair/B2BSystem", "icon": null, "url": "repair/B2BSystem", "oinRel": false, "oinRelCode": null, "orderValue": 3, "children": [{"id": 3202293, "title": "维修订单", "hide": false, "need": false, "letter": "repair/B2BSystem/planTrail", "icon": null, "url": "repair/B2BSystem/planTrail", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": [{"id": 3202296, "title": "维修订单处理", "hide": false, "need": false, "letter": "repair/B2BSystem/planTrail/planDispose", "icon": null, "url": "repair/B2BSystem/planTrail/planDispose", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}]}, {"id": 3202294, "title": "维修报价", "hide": false, "need": false, "letter": "repair/B2BSystem/quote", "icon": null, "url": "repair/B2BSystem/quote", "oinRel": false, "oinRelCode": null, "orderValue": 1, "children": [{"id": 3202297, "title": "待维修报价", "hide": false, "need": false, "letter": "repair/B2BSystem/quote/quoteList", "icon": null, "url": "repair/B2BSystem/quote/quoteList", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}]}, {"id": 3202295, "title": "维修发货单", "hide": false, "need": false, "letter": "repair/B2BSystem/dispatchBill", "icon": null, "url": "repair/B2BSystem/dispatchBill", "oinRel": false, "oinRelCode": null, "orderValue": 2, "children": [{"id": 3202298, "title": "待维修清单", "hide": false, "need": false, "letter": "repair/B2BSystem/dispatchBill/toRepair", "icon": null, "url": "repair/B2BSystem/dispatchBill/toRepair", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}, {"id": 3202299, "title": "维修发货单列表", "hide": false, "need": false, "letter": "repair/B2BSystem/dispatchBill/repairDeliverOrder", "icon": null, "url": "repair/B2BSystem/dispatchBill/repairDeliverOrder", "oinRel": false, "oinRelCode": null, "orderValue": 1, "children": null}, {"id": 3202300, "title": "维修送货明细查询", "hide": false, "need": false, "letter": "repair/B2BSystem/dispatchBill/repairDeliverDetail", "icon": null, "url": "repair/B2BSystem/dispatchBill/repairDeliverDetail", "oinRel": false, "oinRelCode": null, "orderValue": 2, "children": null}]}]}, {"id": 10038, "title": "订单管理", "hide": false, "need": false, "letter": "1", "icon": null, "url": "1", "oinRel": false, "oinRelCode": null, "orderValue": 4, "children": [{"id": 10108, "title": "无合同订单", "hide": false, "need": false, "letter": "scmB2BPendrequests", "icon": null, "url": "scmB2BPendrequests", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}, {"id": 3201739, "title": "订单查询", "hide": false, "need": false, "letter": "scmPoSearch", "icon": null, "url": "scmPoSearch", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}, {"id": 10090, "title": "待处理清单", "hide": false, "need": false, "letter": "scmB2BPendrequests2", "icon": null, "url": "scmB2BPendrequests2", "oinRel": false, "oinRelCode": null, "orderValue": 1, "children": null}, {"id": 10056, "title": "采购合同", "hide": false, "need": false, "letter": "B2BContractList", "icon": null, "url": "B2BContractList", "oinRel": false, "oinRelCode": null, "orderValue": 2, "children": null}, {"id": 10039, "title": "采购订单", "hide": false, "need": false, "letter": "puB2bPoD", "icon": null, "url": "puB2bPoD", "oinRel": false, "oinRelCode": null, "orderValue": 3, "children": null}]}, {"id": 10106, "title": "发货管理", "hide": false, "need": false, "letter": "3", "icon": null, "url": "3", "oinRel": false, "oinRelCode": null, "orderValue": 5, "children": [{"id": 10109, "title": "待发货订单", "hide": false, "need": false, "letter": "puB2bDeliver", "icon": null, "url": "puB2bDeliver", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}, {"id": 10110, "title": "发货中订单", "hide": false, "need": false, "letter": "puInvoiceDeliver", "icon": null, "url": "puInvoiceDeliver", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}, {"id": 10111, "title": "已完成订单", "hide": false, "need": false, "letter": "puInvoiceI", "icon": null, "url": "puInvoiceI", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}, {"id": 10112, "title": "发货单", "hide": false, "need": false, "letter": "puInvoiceH", "icon": null, "url": "puInvoiceH", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}]}, {"id": 3202342, "title": "操作指引", "hide": false, "need": false, "letter": "http://dime-doc.newpearl.com/docs/b2b", "icon": null, "url": "http://dime-doc.newpearl.com/docs/b2b", "oinRel": false, "oinRelCode": null, "orderValue": 99, "children": null}, {"id": 10049, "title": "报表查询", "hide": false, "need": false, "letter": "B2BBillScan", "icon": null, "url": "B2BBillScan", "oinRel": false, "oinRelCode": null, "orderValue": 99, "children": [{"id": 3203095, "title": "开票查询", "hide": false, "need": false, "letter": "b2bInvoiceReport", "icon": null, "url": "/b2bInvoiceReport", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}, {"id": 3201601, "title": "寄存库存查询", "hide": false, "need": false, "letter": "scmB2BConsignmentStockNew", "icon": null, "url": "scmB2BConsignmentStockNew", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}, {"id": 3201602, "title": "寄仓出入库查询", "hide": false, "need": false, "letter": "scmB2BStockScanNew", "icon": null, "url": "scmB2BStockScanNew", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}, {"id": 10057, "title": "寄存仓月报列表", "hide": false, "need": false, "letter": "scmB2BWarehouseMonthBill", "icon": null, "url": "scmB2BWarehouseMonthBill", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}, {"id": 3201708, "title": "订单查询", "hide": false, "need": false, "letter": "scmPoSearch", "icon": null, "url": "scmPoSearch", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}, {"id": 3201737, "title": "供应商B2B权限查询", "hide": false, "need": false, "letter": "b2bPrivileges", "icon": null, "url": "b2bPrivileges", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}, {"id": 3200250, "title": "纸箱期间计划报表", "hide": false, "need": false, "letter": "cartonDurationPlanReport", "icon": null, "url": "/cartonDurationPlanReport", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}, {"id": 10050, "title": "寄存库存查询(旧)", "hide": false, "need": false, "letter": "scmB2BConsignmentStock", "icon": null, "url": "scmB2BConsignmentStock", "oinRel": false, "oinRelCode": null, "orderValue": 1, "children": null}, {"id": 10051, "title": "采购结算报表", "hide": false, "need": false, "letter": "scmB2BPurchaseSettlement", "icon": null, "url": "scmB2BPurchaseSettlement", "oinRel": false, "oinRelCode": null, "orderValue": 2, "children": null}, {"id": 10052, "title": "寄仓出入库查询(旧)", "hide": false, "need": false, "letter": "scmB2BStockScan", "icon": null, "url": "scmB2BStockScan", "oinRel": false, "oinRelCode": null, "orderValue": 5, "children": null}, {"id": 10093, "title": "发货单明细报表", "hide": false, "need": false, "letter": "invoiceDetailReport", "icon": null, "url": "invoiceDetailReport", "oinRel": false, "oinRelCode": null, "orderValue": 5, "children": null}]}]}, {"id": 10001, "title": "数据管理", "hide": false, "need": false, "letter": "meta", "icon": "icon-shuju<PERSON><PERSON><PERSON>", "url": "meta", "oinRel": false, "oinRelCode": null, "orderValue": 6, "children": [{"id": 3203320, "title": "常量管理", "hide": false, "need": false, "letter": "metaDataReport/DIMConstant", "icon": null, "url": "metaDataReport/DIMConstant", "oinRel": false, "oinRelCode": null, "orderValue": 1, "children": null}, {"id": 10232, "title": "公告管理", "hide": false, "need": false, "letter": "notice", "icon": null, "url": "notice", "oinRel": false, "oinRelCode": null, "orderValue": 2, "children": null}, {"id": 10063, "title": "系统日志", "hide": false, "need": false, "letter": "logOperation", "icon": null, "url": "logOperation", "oinRel": false, "oinRelCode": null, "orderValue": 5, "children": null}, {"id": 10210, "title": "采购订单调整", "hide": false, "need": false, "letter": "pms/order/change", "icon": null, "url": "pms/order/change", "oinRel": false, "oinRelCode": null, "orderValue": 7, "children": null}, {"id": 10078, "title": "员工查询", "hide": false, "need": false, "letter": "metaDataReport/employee", "icon": null, "url": "metaDataReport/employee", "oinRel": false, "oinRelCode": null, "orderValue": 10, "children": null}, {"id": 10214, "title": "表单列表", "hide": false, "need": false, "letter": "metaDataFormList", "icon": null, "url": "metaDataFormList", "oinRel": false, "oinRelCode": null, "orderValue": 10, "children": null}]}, {"id": 10000, "title": "权限配置", "hide": false, "need": false, "letter": "sys", "icon": "icon-quan<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "sys", "oinRel": false, "oinRelCode": null, "orderValue": 7, "children": [{"id": 10094, "title": "用户中心列表", "hide": false, "need": false, "letter": "misUserCenter", "icon": null, "url": "misUserCenter", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}]}, {"id": 10134, "title": "需求登记", "hide": false, "need": false, "letter": "requirement", "icon": "icon-B2Bxitong", "url": "requirement", "oinRel": false, "oinRelCode": null, "orderValue": 8, "children": [{"id": 10135, "title": "需求登记", "hide": false, "need": false, "letter": "demand", "icon": null, "url": "demand", "oinRel": false, "oinRelCode": null, "orderValue": 1, "children": null}]}, {"id": 3202340, "title": "集团内部操作指引", "hide": false, "need": false, "letter": "dim-doc", "icon": null, "url": "dim-doc", "oinRel": false, "oinRelCode": null, "orderValue": 99, "children": [{"id": 3202341, "title": "DIM系统操作指引", "hide": false, "need": false, "letter": "https://dim-doc.newpearl.com/docs/dim", "icon": null, "url": "https://dim-doc.newpearl.com/docs/dim", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}, {"id": 3203423, "title": "执行接口", "hide": false, "need": false, "letter": "runInterface", "icon": null, "url": "runInterface", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}]}]