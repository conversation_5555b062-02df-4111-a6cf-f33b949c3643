[{"id": 3203348, "title": "物料供应周期表", "hide": false, "need": false, "letter": "matSupplyCycle", "icon": null, "url": "matSupplyCycle", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}, {"id": 3203354, "title": "供应商分单逻辑表", "hide": false, "need": false, "letter": "supSplit<PERSON>rderLogic", "icon": null, "url": "supSplit<PERSON>rderLogic", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}, {"id": 10028, "title": "辅助属性类型", "hide": false, "need": false, "letter": "mmsatType", "icon": null, "url": "mmsatType", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}, {"id": 3202359, "title": "数据看板", "hide": false, "need": false, "letter": "databoard", "icon": null, "url": "databoard", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}, {"id": 3201850, "title": "系统树结构", "hide": false, "need": false, "letter": "sysTree", "icon": null, "url": "sysTree", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}, {"id": 3202411, "title": "物料主档图片", "hide": false, "need": false, "letter": "matPicImport", "icon": null, "url": "matPicImport", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}, {"id": 3202677, "title": "用户默认首页设置", "hide": false, "need": false, "letter": "userIndex", "icon": null, "url": "userIndex", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}, {"id": 3202679, "title": "管理层数据看板", "hide": false, "need": false, "letter": "depot/dataBoard", "icon": null, "url": "depot/dataBoard", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}, {"id": 3201725, "title": "结算账户审批", "hide": false, "need": false, "letter": "bankAccountAudit", "icon": null, "url": "bankAccountAudit", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}, {"id": 3201222, "title": "库位", "hide": false, "need": false, "letter": "wms/base/storagebin", "icon": null, "url": "wms/base/storagebin", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}, {"id": 3201223, "title": "物料库位排序", "hide": false, "need": false, "letter": "wms/base/storageSort", "icon": null, "url": "wms/base/storageSort", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}, {"id": 3203283, "title": "园区对应工厂", "hide": false, "need": false, "letter": "metaDataReport/parkTofactory", "icon": null, "url": "metaDataReport/parkTofactory", "oinRel": false, "oinRelCode": null, "orderValue": 0, "children": null}, {"id": 10029, "title": "物料分类", "hide": false, "need": false, "letter": "materielType", "icon": null, "url": "materielType", "oinRel": false, "oinRelCode": null, "orderValue": 1, "children": null}, {"id": 3201307, "title": "物料主档（配置化）", "hide": false, "need": false, "letter": "materiel<PERSON><PERSON><PERSON>ew", "icon": null, "url": "materiel<PERSON><PERSON><PERSON>ew", "oinRel": false, "oinRelCode": null, "orderValue": 2, "children": null}, {"id": 10030, "title": "物料主档", "hide": false, "need": false, "letter": "materielMain", "icon": null, "url": "materielMain", "oinRel": false, "oinRelCode": null, "orderValue": 2, "children": null}, {"id": 10065, "title": "合同关系链", "hide": false, "need": false, "letter": "scmPurchaseContractHCreateFactory", "icon": null, "url": "scmPurchaseContractHCreateFactory", "oinRel": false, "oinRelCode": null, "orderValue": 2, "children": null}, {"id": 10069, "title": "物料编号申请", "hide": false, "need": false, "letter": "scmMaterielCodeApply", "icon": null, "url": "scmMaterielCodeApply", "oinRel": false, "oinRelCode": null, "orderValue": 3, "children": null}, {"id": 10089, "title": "物料编码申请明细", "hide": false, "need": false, "letter": "scmMaterielCodeApply/Detail", "icon": null, "url": "scmMaterielCodeApply/Detail", "oinRel": false, "oinRelCode": null, "orderValue": 4, "children": null}, {"id": 10070, "title": "物料编码申请处理", "hide": false, "need": false, "letter": "scmMaterielCodeApply/Approval", "icon": null, "url": "scmMaterielCodeApply/Approval", "oinRel": false, "oinRelCode": null, "orderValue": 5, "children": null}, {"id": 10118, "title": "物料主档处理", "hide": false, "need": false, "letter": "scmMaterielMainHandle", "icon": null, "url": "scmMaterielMainHandle", "oinRel": false, "oinRelCode": null, "orderValue": 5, "children": null}, {"id": 10031, "title": "供应商类型", "hide": false, "need": false, "letter": "scmBaseType", "icon": null, "url": "scmBaseType", "oinRel": false, "oinRelCode": null, "orderValue": 6, "children": null}, {"id": 10032, "title": "供应商档案", "hide": false, "need": false, "letter": "scmBaseFile", "icon": null, "url": "scmBaseFile", "oinRel": false, "oinRelCode": null, "orderValue": 7, "children": null}, {"id": 10098, "title": "供应商档案处理", "hide": false, "need": false, "letter": "scmBaseFileAuditList", "icon": null, "url": "/scmBaseFileAuditList", "oinRel": false, "oinRelCode": null, "orderValue": 8, "children": null}, {"id": 10213, "title": "供应商评分模板维护", "hide": false, "need": false, "letter": "supScoreTemplate", "icon": null, "url": "supScoreTemplate", "oinRel": false, "oinRelCode": null, "orderValue": 9, "children": null}, {"id": 10079, "title": "物料种类列表", "hide": false, "need": false, "letter": "metaDataReport/typeOfMaterial", "icon": null, "url": "metaDataReport/typeOfMaterial", "oinRel": false, "oinRelCode": null, "orderValue": 10, "children": null}, {"id": 10087, "title": "品牌列表", "hide": false, "need": false, "letter": "omBrand", "icon": null, "url": "omBrand", "oinRel": false, "oinRelCode": null, "orderValue": 10, "children": null}, {"id": 10066, "title": "采购员关系链", "hide": false, "need": false, "letter": "metaDataReport/purchaser", "icon": null, "url": "metaDataReport/purchaser", "oinRel": false, "oinRelCode": null, "orderValue": 11, "children": null}, {"id": 10034, "title": "采购分区与采购员档案维护", "hide": false, "need": false, "letter": "scmPurchaseCltUserInfo", "icon": null, "url": "scmPurchaseCltUserInfo", "oinRel": false, "oinRelCode": null, "orderValue": 12, "children": null}, {"id": 10055, "title": "新建物料", "hide": false, "need": false, "letter": "scmMaterielApply", "icon": null, "url": "scmMaterielApply", "oinRel": false, "oinRelCode": null, "orderValue": 99, "children": null}, {"id": 10068, "title": "计量单位列表", "hide": false, "need": false, "letter": "metaDataReport/uom", "icon": null, "url": "metaDataReport/uom", "oinRel": false, "oinRelCode": null, "orderValue": 99, "children": null}, {"id": 10088, "title": "消息模板", "hide": false, "need": false, "letter": "msgTemplate", "icon": null, "url": "msgTemplate", "oinRel": false, "oinRelCode": null, "orderValue": 99, "children": null}, {"id": 3201202, "title": "物料种类", "hide": false, "need": false, "letter": "baseMatType", "icon": null, "url": "baseMatType", "oinRel": false, "oinRelCode": null, "orderValue": 99, "children": null}, {"id": 3201266, "title": "物料分类厚度关系表", "hide": false, "need": false, "letter": "npMatgroupThicknessRelationReport", "icon": null, "url": "npMatgroupThicknessRelationReport", "oinRel": false, "oinRelCode": null, "orderValue": 99, "children": null}]