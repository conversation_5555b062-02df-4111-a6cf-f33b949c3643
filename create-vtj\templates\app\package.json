{"name": "vtj-app-template", "description": "VTJ.PRO", "private": true, "version": "0.9.0", "type": "module", "scripts": {"setup": "npm install --unsafe-perm --registry=https://registry.npmmirror.com", "dev": "cross-env ENV_TYPE=local vite", "build": "npm run build:prod", "build:sit": "vue-tsc && cross-env ENV_TYPE=sit vite build", "build:uat": "vue-tsc && cross-env ENV_TYPE=uat vite build", "build:pre": "vue-tsc && cross-env ENV_TYPE=pre vite build", "build:prod": "vue-tsc && cross-env ENV_TYPE=live vite build", "preview": "vite preview", "clean": "node ./scripts/clean.mjs"}, "dependencies": {"@vtj/web": "latest", "vue": "~3.5.0", "vue-router": "~4.5.0"}, "devDependencies": {"@vtj/cli": "latest", "@vtj/pro": "latest"}}