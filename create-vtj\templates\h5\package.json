{"name": "vtj-app-template", "description": "H5项目工程", "private": true, "version": "0.9.0", "type": "module", "scripts": {"setup": "pnpm install --unsafe-perm --registry=https://registry.npmmirror.com", "clean": "vtj rm ./package-lock.json && vtj rm ./pnpm-lock.yaml && vtj rm ./dist && vtj rm ./node_modules", "dev": "cross-env ENV_TYPE=local vite", "build": "pnpm run build:prod", "build:dev": "vue-tsc && cross-env ENV_TYPE=dev vite build", "build:sit": "vue-tsc && cross-env ENV_TYPE=sit vite build", "build:uat": "vue-tsc && cross-env ENV_TYPE=uat vite build", "build:pre": "vue-tsc && cross-env ENV_TYPE=pre vite build", "build:prod": "vue-tsc && cross-env ENV_TYPE=live vite build", "preview": "vite preview"}, "dependencies": {"@vtj/renderer": "latest", "@vtj/h5": "latest", "vue": "~3.5.5", "vue-router": "~4.5.0"}, "devDependencies": {"@vtj/cli": "latest", "@vtj/pro": "latest"}, "vtj": {"platform": "h5"}}